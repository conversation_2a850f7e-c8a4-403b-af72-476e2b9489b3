{"name": "@sveltejs/vite-plugin-svelte-inspector", "version": "1.0.4", "license": "MIT", "author": "dominikg", "files": ["src"], "type": "module", "types": "src/index.d.ts", "exports": {".": {"types": "./src/index.d.ts", "import": "./src/index.js"}}, "engines": {"node": "^14.18.0 || >= 16"}, "repository": {"type": "git", "url": "git+https://github.com/sveltejs/vite-plugin-svelte.git", "directory": "packages/vite-plugin-svelte-inspector"}, "keywords": ["vite-plugin", "vite plugin", "vite", "svelte"], "bugs": {"url": "https://github.com/sveltejs/vite-plugin-svelte/issues"}, "homepage": "https://github.com/sveltejs/vite-plugin-svelte#readme", "dependencies": {"debug": "^4.3.4"}, "peerDependencies": {"@sveltejs/vite-plugin-svelte": "^2.2.0", "svelte": "^3.54.0 || ^4.0.0", "vite": "^4.0.0"}, "devDependencies": {"@types/debug": "^4.1.8", "svelte": "^4.1.2", "vite": "^4.4.9"}, "scripts": {"check:publint": "publint --strict", "check:types": "tsc --noEmit"}}
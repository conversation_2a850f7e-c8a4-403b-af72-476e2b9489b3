{"name": "@sveltejs/kit", "version": "1.30.4", "description": "The fastest way to build Svelte apps", "repository": {"type": "git", "url": "https://github.com/sveltejs/kit", "directory": "packages/kit"}, "license": "MIT", "homepage": "https://kit.svelte.dev", "type": "module", "dependencies": {"@sveltejs/vite-plugin-svelte": "^2.5.0", "@types/cookie": "^0.5.1", "cookie": "^0.5.0", "devalue": "^4.3.1", "esm-env": "^1.0.0", "kleur": "^4.1.5", "magic-string": "^0.30.0", "mrmime": "^1.0.1", "sade": "^1.8.1", "set-cookie-parser": "^2.6.0", "sirv": "^2.0.2", "tiny-glob": "^0.2.9", "undici": "^5.28.3"}, "devDependencies": {"@playwright/test": "1.30.0", "@types/connect": "^3.4.35", "@types/node": "^16.18.6", "@types/sade": "^1.7.4", "@types/set-cookie-parser": "^2.4.2", "dts-buddy": "^0.2.4", "rollup": "^3.29.4", "svelte": "^4.2.7", "svelte-preprocess": "^5.1.1", "typescript": "^4.9.4", "vite": "^4.4.9", "vitest": "^0.34.5"}, "peerDependencies": {"svelte": "^3.54.0 || ^4.0.0-next.0 || ^5.0.0-next.0", "vite": "^4.0.0"}, "bin": {"svelte-kit": "svelte-kit.js"}, "files": ["src", "!src/**/*.spec.js", "!src/core/**/fixtures", "!src/core/**/test", "types", "svelte-kit.js", "postinstall.js"], "exports": {"./package.json": "./package.json", ".": {"types": "./types/index.d.ts", "import": "./src/exports/index.js"}, "./node": {"types": "./types/index.d.ts", "import": "./src/exports/node/index.js"}, "./node/polyfills": {"types": "./types/index.d.ts", "import": "./src/exports/node/polyfills.js"}, "./hooks": {"types": "./types/index.d.ts", "import": "./src/exports/hooks/index.js"}, "./vite": {"types": "./types/index.d.ts", "import": "./src/exports/vite/index.js"}}, "types": "types/index.d.ts", "engines": {"node": "^16.14 || >=18"}, "scripts": {"lint": "prettier --config ../../.prettierrc --check .", "check": "tsc", "check:all": "tsc && pnpm -r --filter=\"./**\" check", "format": "prettier --config ../../.prettierrc --write .", "test": "pnpm test:unit && pnpm test:integration", "test:integration": "pnpm -r --workspace-concurrency 1 --filter=\"./test/**\" test", "test:cross-platform:dev": "pnpm -r --workspace-concurrency 1 --filter=\"./test/**\" test:cross-platform:dev", "test:cross-platform:build": "pnpm test:unit && pnpm -r --workspace-concurrency 1 --filter=\"./test/**\" test:cross-platform:build", "test:unit": "vitest --config kit.vitest.config.js run", "postinstall": "node postinstall.js", "generate:version": "node scripts/generate-version.js", "generate:types": "node scripts/generate-dts.js"}}
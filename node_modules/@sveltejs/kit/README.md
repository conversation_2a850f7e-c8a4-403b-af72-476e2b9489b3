# The fastest way to build Svelte apps

This is the [SvelteKit](https://kit.svelte.dev) framework and CLI.

The quickest way to get started is via the [create-svelte](https://github.com/sveltejs/kit/tree/master/packages/create-svelte) package:

```bash
npm create svelte@latest my-app
cd my-app
npm install
npm run dev
```

See the [documentation](https://kit.svelte.dev/docs) to learn more.

## Changelog

[The Changelog for this package is available on GitHub](https://github.com/sveltejs/kit/blob/master/packages/kit/CHANGELOG.md).

{"name": "@sveltejs/vite-plugin-svelte", "version": "2.5.3", "license": "MIT", "author": "dominikg", "files": ["src"], "type": "module", "types": "src/index.d.ts", "exports": {".": {"types": "./src/index.d.ts", "import": "./src/index.js"}, "./package.json": "./package.json"}, "engines": {"node": "^14.18.0 || >= 16"}, "repository": {"type": "git", "url": "git+https://github.com/sveltejs/vite-plugin-svelte.git", "directory": "packages/vite-plugin-svelte"}, "keywords": ["vite-plugin", "vite plugin", "vite", "svelte"], "bugs": {"url": "https://github.com/sveltejs/vite-plugin-svelte/issues"}, "homepage": "https://github.com/sveltejs/vite-plugin-svelte#readme", "dependencies": {"@sveltejs/vite-plugin-svelte-inspector": "^1.0.4", "debug": "^4.3.4", "deepmerge": "^4.3.1", "kleur": "^4.1.5", "magic-string": "^0.30.3", "svelte-hmr": "^0.15.3", "vitefu": "^0.2.4"}, "peerDependencies": {"svelte": "^3.54.0 || ^4.0.0 || ^5.0.0-next.0", "vite": "^4.0.0"}, "devDependencies": {"@types/debug": "^4.1.8", "esbuild": "^0.19.3", "svelte": "^4.2.0", "vite": "^4.4.9"}, "scripts": {"check:publint": "publint --strict", "check:types": "tsc --noEmit"}}
{"name": "periscopic", "description": "periscopic", "version": "3.1.0", "repository": "Rich-Harris/periscopic", "main": "src/index.js", "module": "src/index.js", "type": "module", "exports": {"types": "./types/index.js", "import": "./src/index.js"}, "types": "types/index.d.ts", "files": ["src", "types"], "devDependencies": {"acorn": "^8.0.0", "typescript": "^4.9.0", "uvu": "^0.5.1"}, "scripts": {"test": "uvu test", "prepublishOnly": "npm test && tsc"}, "license": "MIT", "dependencies": {"@types/estree": "^1.0.0", "estree-walker": "^3.0.0", "is-reference": "^3.0.0"}}
{"name": "globrex", "version": "0.1.2", "description": "Glob to regular expression with support for extended globs", "main": "index.js", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/terkelg/globrex"}, "files": ["index.js"], "keywords": ["glob", "regex", "regexp", "parser", "glob2regx", "compiler"], "scripts": {"test": "tape test/*.js | tap-spec"}, "devDependencies": {"tap-spec": "^4.1.1", "tape": "^4.8.0"}}